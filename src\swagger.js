const swaggerAutogen = require('swagger-autogen')();

const outputFile = './swagger-output.json';
const endpointsFiles = [
    './src/routes/ambientesRoutes.js',
    './src/routes/edificiosRoutes.js',
    './src/routes/equipes_inspecaoRoutes.js',
    './src/routes/fotosRoutes.js',
    './src/routes/funcoes_usuarioRoutes.js',
    './src/routes/inspecoesRoutes.js',
    './src/routes/patologiasRoutes.js',
    './src/routes/pavimentosRoutes.js',
    './src/routes/relatoriosRoutes.js',
    './src/routes/sistemasRoutes.js',
    './src/routes/userRoutes.js',
    './src/routes/usuarioRoutes.js', // Não sei qual tá certo
    './src/server.js'
];

const doc = {
    info: {
        version: '1.0.0',
        title: 'API do Projeto',
        description: 'Documentação da API, gerada automaticamente pelo swagger-autogen.'
    },
    host: 'localhost:3000',
    basePath: '/',
    schemes: ['http', 'https'],
    consumes: ['application/json'],
    produces: ['application/json'],
    tags: [
        {
            name: 'Usuarios',
            description: 'Endpoints relacionados a usuários'
        },
        {
            name: 'Inspecoes',
            description: 'Endpoints relacionados a inspeções'
        }
        // Adicionar mais tags
    ],
    definitions: { // Definir os schemas de dados
        Usuario: {
            type: 'object',
            properties: {
                id: { type: 'integer', example: 1 },
                nome: { type: 'string', example: 'João da Silva' },
                email: { type: 'string', example: '<EMAIL>' },
                criado_em: { type: 'string', format: 'date-time', example: '2024-07-22T14:30:00Z' }
            }
        },
        Inspecao: {
            type: 'object',
            properties: {
                id: { type: 'integer', example: 1 },
                projeto_id: { type: 'integer', example: 101 },
                titulo: { type: 'string', example: 'Inspeção Edifício Central' },
                nome_edificio: { type: 'string', example: 'Edifício Central' },
                endereco: { type: 'string', example: 'Rua Principal, 123' },
                tipo_edificio: { type: 'string', example: 'Comercial' },
                torre_bloco: { type: 'string', example: 'Bloco A' },
                data_inicio: { type: 'string', format: 'date', example: '2024-08-01' },
                data_fim: { type: 'string', format: 'date', example: '2024-08-05' },
                status: { type: 'string', example: 'em_andamento' },
                criado_por: { type: 'integer', example: 1 }
            }
        }
        // Adicione mais definições de modelos
    }
};

swaggerAutogen(outputFile, endpointsFiles, doc).then(() => {
    require('./server.js');
});
